"""
简化版向量化管理模块
使用Google Generative AI的embedding功能作为备选方案
"""

import numpy as np
from typing import List, Union
import pickle
import os

try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    print("⚠️ sentence-transformers 不可用，将使用简化版本")

try:
    import google.generativeai as genai
    GOOGLE_AI_AVAILABLE = True
except ImportError:
    GOOGLE_AI_AVAILABLE = False

class EmbeddingManager:
    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        """
        初始化向量化管理器
        """
        self.model_name = model_name
        self.model = None
        self.embedding_dim = 384  # 默认维度
        self.load_model()
    
    def load_model(self):
        """加载向量化模型"""
        if SENTENCE_TRANSFORMERS_AVAILABLE:
            try:
                print(f"Loading sentence-transformers model: {self.model_name}")
                self.model = SentenceTransformer(self.model_name)
                self.embedding_dim = self.model.get_sentence_embedding_dimension()
                print(f"Model loaded successfully. Embedding dimension: {self.embedding_dim}")
                return
            except Exception as e:
                print(f"Error loading sentence-transformers: {e}")
        
        # 备选方案：使用简单的TF-IDF向量化
        print("Using simple TF-IDF vectorization as fallback")
        self._init_simple_vectorizer()
    
    def _init_simple_vectorizer(self):
        """初始化简单的向量化器"""
        try:
            from sklearn.feature_extraction.text import TfidfVectorizer
            self.model = TfidfVectorizer(max_features=384, stop_words='english')
            self.embedding_dim = 384
            self._is_fitted = False
            print("Initialized TF-IDF vectorizer")
        except ImportError:
            print("sklearn not available, using basic word counting")
            self.model = None
            self.embedding_dim = 100
    
    def encode_texts(self, texts: List[str], batch_size: int = 32, show_progress: bool = True) -> np.ndarray:
        """
        将文本列表转换为向量
        """
        if not texts:
            return np.array([])
        
        if SENTENCE_TRANSFORMERS_AVAILABLE and hasattr(self.model, 'encode'):
            try:
                print(f"Encoding {len(texts)} texts with sentence-transformers...")
                embeddings = self.model.encode(
                    texts, 
                    batch_size=batch_size, 
                    show_progress_bar=show_progress,
                    convert_to_numpy=True
                )
                print(f"Encoding completed. Shape: {embeddings.shape}")
                return embeddings
            except Exception as e:
                print(f"Error with sentence-transformers: {e}")
        
        # 备选方案
        return self._encode_simple(texts)
    
    def _encode_simple(self, texts: List[str]) -> np.ndarray:
        """简单的向量化方法"""
        try:
            from sklearn.feature_extraction.text import TfidfVectorizer
            if not hasattr(self, '_is_fitted') or not self._is_fitted:
                self.model = TfidfVectorizer(max_features=384, stop_words='english')
                embeddings = self.model.fit_transform(texts).toarray()
                self._is_fitted = True
            else:
                embeddings = self.model.transform(texts).toarray()
            
            print(f"Simple encoding completed. Shape: {embeddings.shape}")
            return embeddings
        except ImportError:
            # 最基本的词频向量化
            return self._encode_basic(texts)
    
    def _encode_basic(self, texts: List[str]) -> np.ndarray:
        """最基本的向量化方法"""
        print("Using basic word frequency encoding...")
        
        # 构建词汇表
        vocab = set()
        for text in texts:
            words = text.lower().split()
            vocab.update(words)
        
        vocab = list(vocab)[:self.embedding_dim]  # 限制词汇表大小
        word_to_idx = {word: idx for idx, word in enumerate(vocab)}
        
        # 创建向量
        embeddings = []
        for text in texts:
            vector = np.zeros(len(vocab))
            words = text.lower().split()
            for word in words:
                if word in word_to_idx:
                    vector[word_to_idx[word]] += 1
            
            # 归一化
            if np.linalg.norm(vector) > 0:
                vector = vector / np.linalg.norm(vector)
            
            embeddings.append(vector)
        
        embeddings = np.array(embeddings)
        print(f"Basic encoding completed. Shape: {embeddings.shape}")
        return embeddings
    
    def encode_single_text(self, text: str) -> np.ndarray:
        """向量化单个文本"""
        embeddings = self.encode_texts([text])
        return embeddings[0] if len(embeddings) > 0 else np.array([])
    
    def compute_similarity(self, query_embedding: np.ndarray, document_embeddings: np.ndarray) -> np.ndarray:
        """计算相似度"""
        try:
            # 使用余弦相似度
            query_norm = np.linalg.norm(query_embedding)
            doc_norms = np.linalg.norm(document_embeddings, axis=1)
            
            if query_norm == 0:
                return np.zeros(len(document_embeddings))
            
            dot_products = np.dot(document_embeddings, query_embedding)
            similarities = dot_products / (doc_norms * query_norm + 1e-8)
            
            return similarities
        except Exception as e:
            print(f"Error computing similarity: {e}")
            return np.array([])
    
    def find_most_similar(self, query: str, document_texts: List[str], 
                         document_embeddings: np.ndarray = None, top_k: int = 5) -> List[tuple]:
        """找到最相似的文档"""
        try:
            query_embedding = self.encode_single_text(query)
            
            if document_embeddings is None:
                document_embeddings = self.encode_texts(document_texts)
            
            similarities = self.compute_similarity(query_embedding, document_embeddings)
            top_indices = np.argsort(similarities)[::-1][:top_k]
            
            results = []
            for idx in top_indices:
                results.append((
                    similarities[idx],
                    idx,
                    document_texts[idx] if idx < len(document_texts) else ""
                ))
            
            return results
        except Exception as e:
            print(f"Error finding similar documents: {e}")
            return []

if __name__ == "__main__":
    # 测试
    embedding_manager = EmbeddingManager()
    
    test_texts = [
        "This is a great Chinese restaurant with delicious food",
        "Thai cuisine with spicy tom yam soup",
        "Italian pizza place with authentic flavors"
    ]
    
    embeddings = embedding_manager.encode_texts(test_texts)
    print(f"Generated embeddings shape: {embeddings.shape}")
    
    query = "Looking for good Chinese food"
    results = embedding_manager.find_most_similar(query, test_texts, embeddings, top_k=2)
    
    print(f"\nQuery: {query}")
    print("Most similar documents:")
    for score, idx, text in results:
        print(f"Score: {score:.4f}, Index: {idx}, Text: {text}")
