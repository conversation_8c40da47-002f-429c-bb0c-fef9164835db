"""
RAG系统安装和设置脚本
"""

import os
import subprocess
import sys
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要Python 3.8或更高版本")
        return False
    print(f"✅ Python版本: {sys.version}")
    return True

def install_requirements():
    """安装依赖包"""
    print("📦 安装Python依赖包...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖包安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False

def setup_env_file():
    """设置环境变量文件"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists():
        if env_example.exists():
            # 复制示例文件
            with open(env_example, 'r') as f:
                content = f.read()
            with open(env_file, 'w') as f:
                f.write(content)
            print("✅ 创建了.env文件")
        else:
            # 创建基本的.env文件
            with open(env_file, 'w') as f:
                f.write("# Google Gemini API Key\n")
                f.write("GOOGLE_API_KEY=your_gemini_api_key_here\n")
                f.write("\n# Optional: ChromaDB settings\n")
                f.write("CHROMA_DB_PATH=./chroma_db\n")
            print("✅ 创建了.env文件")
    else:
        print("✅ .env文件已存在")
    
    return True

def check_data_file():
    """检查数据文件"""
    data_file = Path("vertex_ai_training_data.jsonl")
    if data_file.exists():
        print("✅ 找到数据文件 vertex_ai_training_data.jsonl")
        return True
    else:
        print("⚠️  未找到数据文件 vertex_ai_training_data.jsonl")
        print("   请确保数据文件在当前目录下")
        return False

def create_directories():
    """创建必要的目录"""
    directories = ["chroma_db", "logs"]
    for dir_name in directories:
        Path(dir_name).mkdir(exist_ok=True)
    print("✅ 创建了必要的目录")
    return True

def main():
    """主安装函数"""
    print("🚀 开始设置RAG系统...\n")
    
    steps = [
        ("检查Python版本", check_python_version),
        ("安装依赖包", install_requirements),
        ("设置环境文件", setup_env_file),
        ("检查数据文件", check_data_file),
        ("创建目录", create_directories)
    ]
    
    all_success = True
    
    for step_name, step_func in steps:
        print(f"\n🔧 {step_name}...")
        if not step_func():
            all_success = False
    
    print("\n" + "="*50)
    
    if all_success:
        print("🎉 RAG系统设置完成！")
        print("\n📝 下一步:")
        print("1. 编辑.env文件，添加你的Google Gemini API密钥")
        print("2. 运行测试: python test_system.py")
        print("3. 启动Web界面: streamlit run streamlit_app.py")
        print("4. 或运行命令行版本: python rag_system.py")
        
        print("\n🔑 获取API密钥:")
        print("访问 https://makersuite.google.com/app/apikey")
        print("登录Google账户并创建新的API密钥")
        
    else:
        print("❌ 设置过程中出现错误，请检查上述问题")
    
    print("="*50)

if __name__ == "__main__":
    main()
