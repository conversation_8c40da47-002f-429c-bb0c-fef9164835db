# 🍽️ 马来西亚餐厅RAG系统

基于Google Gemini API和向量数据库的智能餐厅推荐系统，可以回答关于马来西亚餐厅的各种问题。

## 🌟 功能特点

- **智能问答**: 使用RAG技术结合Gemini API回答餐厅相关问题
- **向量搜索**: 基于语义相似度的餐厅搜索
- **多语言支持**: 支持中文、英文、马来文查询
- **Web界面**: 友好的Streamlit界面
- **数据可视化**: 餐厅数据统计和分析

## 🏗️ 系统架构

```
用户查询 → 向量化 → 向量数据库搜索 → 检索相关文档 → Gemini API生成回答
```

### 核心组件

1. **数据预处理** (`data_processor.py`): 从JSONL文件提取和清理餐厅数据
2. **向量化管理** (`embedding_manager.py`): 使用sentence-transformers进行文本向量化
3. **向量数据库** (`vector_database.py`): 使用ChromaDB存储和检索向量
4. **Gemini客户端** (`gemini_client.py`): 与Google Gemini API交互
5. **RAG系统** (`rag_system.py`): 整合所有组件的主系统
6. **Web界面** (`streamlit_app.py`): Streamlit用户界面

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆或下载项目文件
# 确保所有Python文件都在同一目录下

# 安装依赖
pip install -r requirements.txt
```

### 2. 获取Gemini API密钥

1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 创建新的API密钥
3. 复制密钥备用

### 3. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，添加你的API密钥
GOOGLE_API_KEY=your_gemini_api_key_here
```

### 4. 准备数据

确保你的 `vertex_ai_training_data.jsonl` 文件在项目根目录下。

### 5. 运行系统

#### 方式一：使用Web界面（推荐）

```bash
streamlit run streamlit_app.py
```

然后在浏览器中打开 `http://localhost:8501`

#### 方式二：使用Python脚本

```python
from rag_system import RAGSystem

# 初始化系统
rag = RAGSystem()

# 设置数据库
rag.setup_database()

# 提问
answer = rag.generate_answer("推荐一家好的中餐厅")
print(answer)
```

## 📖 使用指南

### Web界面使用

1. **设置API密钥**: 在侧边栏输入你的Google API密钥
2. **初始化数据库**: 点击"初始化/重建数据库"按钮
3. **开始提问**: 在"智能问答"标签页输入问题
4. **搜索餐厅**: 在"餐厅搜索"标签页搜索特定餐厅
5. **查看统计**: 在"数据统计"标签页查看数据分析

### 示例查询

- "推荐一家好的中餐厅"
- "有什么清真餐厅推荐？"
- "Beans Cafe怎么样？"
- "哪里有好吃的Tom Yam？"
- "适合商务聚餐的餐厅"

## 🔧 配置选项

### 向量化模型选择

在 `rag_system.py` 中可以更改向量化模型：

```python
# 轻量级，速度快
embedding_model = "all-MiniLM-L6-v2"

# 质量更高，但较慢
embedding_model = "all-mpnet-base-v2"

# 多语言支持（推荐）
embedding_model = "paraphrase-multilingual-MiniLM-L12-v2"
```

### 数据库配置

```python
# 自定义数据库路径
rag = RAGSystem(db_path="./my_custom_db")

# 强制重建数据库
rag.setup_database(force_rebuild=True)
```

## 📊 数据格式

系统期望的JSONL格式：

```json
{
  "contents": [
    {
      "role": "user",
      "parts": [{"text": "用户问题"}]
    },
    {
      "role": "model", 
      "parts": [{"text": "模型回答，包含餐厅信息"}]
    }
  ]
}
```

## 🛠️ 故障排除

### 常见问题

1. **API密钥错误**
   - 确保API密钥正确且有效
   - 检查API配额是否充足

2. **数据库初始化失败**
   - 检查JSONL文件是否存在且格式正确
   - 确保有足够的磁盘空间

3. **向量化模型下载失败**
   - 检查网络连接
   - 尝试使用较小的模型

4. **搜索结果为空**
   - 确保数据库已正确初始化
   - 尝试不同的查询关键词

### 调试模式

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 测试各个组件
from data_processor import DataProcessor
from embedding_manager import EmbeddingManager
from vector_database import VectorDatabase
from gemini_client import GeminiClient

# 逐个测试
processor = DataProcessor("vertex_ai_training_data.jsonl")
data = processor.process_all_data()
print(f"Processed {len(data)} entries")
```

## 📈 性能优化

1. **向量化批处理**: 调整 `batch_size` 参数
2. **缓存向量**: 保存和重用计算好的向量
3. **数据库索引**: ChromaDB自动优化索引
4. **模型选择**: 根据需求选择合适的向量化模型

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License

## 🙏 致谢

- Google Gemini API
- Sentence Transformers
- ChromaDB
- Streamlit
