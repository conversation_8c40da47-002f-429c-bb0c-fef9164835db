"""
向量化管理模块
使用sentence-transformers进行文本向量化
"""

from sentence_transformers import SentenceTransformer
import numpy as np
from typing import List, Union
import pickle
import os

class EmbeddingManager:
    def __init__(self, model_name: str = "all-MiniLM-L6-v2"):
        """
        初始化向量化管理器
        
        Args:
            model_name: 使用的sentence-transformer模型名称
                       推荐模型：
                       - "all-MiniLM-L6-v2": 轻量级，速度快
                       - "all-mpnet-base-v2": 质量更高，但较慢
                       - "paraphrase-multilingual-MiniLM-L12-v2": 支持多语言
        """
        self.model_name = model_name
        self.model = None
        self.load_model()
    
    def load_model(self):
        """加载sentence-transformer模型"""
        try:
            print(f"Loading embedding model: {self.model_name}")
            self.model = SentenceTransformer(self.model_name)
            print(f"Model loaded successfully. Embedding dimension: {self.model.get_sentence_embedding_dimension()}")
        except Exception as e:
            print(f"Error loading model {self.model_name}: {e}")
            # 回退到默认模型
            print("Falling back to default model: all-MiniLM-L6-v2")
            self.model_name = "all-MiniLM-L6-v2"
            self.model = SentenceTransformer(self.model_name)
    
    def encode_texts(self, texts: List[str], batch_size: int = 32, show_progress: bool = True) -> np.ndarray:
        """
        将文本列表转换为向量
        
        Args:
            texts: 要向量化的文本列表
            batch_size: 批处理大小
            show_progress: 是否显示进度
            
        Returns:
            numpy数组，包含所有文本的向量表示
        """
        if not texts:
            return np.array([])
        
        try:
            print(f"Encoding {len(texts)} texts...")
            embeddings = self.model.encode(
                texts, 
                batch_size=batch_size, 
                show_progress_bar=show_progress,
                convert_to_numpy=True
            )
            print(f"Encoding completed. Shape: {embeddings.shape}")
            return embeddings
        except Exception as e:
            print(f"Error during encoding: {e}")
            return np.array([])
    
    def encode_single_text(self, text: str) -> np.ndarray:
        """
        向量化单个文本
        
        Args:
            text: 要向量化的文本
            
        Returns:
            文本的向量表示
        """
        try:
            embedding = self.model.encode([text], convert_to_numpy=True)
            return embedding[0]
        except Exception as e:
            print(f"Error encoding single text: {e}")
            return np.array([])
    
    def save_embeddings(self, embeddings: np.ndarray, file_path: str):
        """保存向量到文件"""
        try:
            with open(file_path, 'wb') as f:
                pickle.dump(embeddings, f)
            print(f"Embeddings saved to {file_path}")
        except Exception as e:
            print(f"Error saving embeddings: {e}")
    
    def load_embeddings(self, file_path: str) -> np.ndarray:
        """从文件加载向量"""
        try:
            with open(file_path, 'rb') as f:
                embeddings = pickle.load(f)
            print(f"Embeddings loaded from {file_path}")
            return embeddings
        except Exception as e:
            print(f"Error loading embeddings: {e}")
            return np.array([])
    
    def compute_similarity(self, query_embedding: np.ndarray, document_embeddings: np.ndarray) -> np.ndarray:
        """
        计算查询向量与文档向量的相似度
        
        Args:
            query_embedding: 查询的向量表示
            document_embeddings: 文档向量矩阵
            
        Returns:
            相似度分数数组
        """
        try:
            # 使用余弦相似度
            query_norm = np.linalg.norm(query_embedding)
            doc_norms = np.linalg.norm(document_embeddings, axis=1)
            
            # 避免除零
            if query_norm == 0:
                return np.zeros(len(document_embeddings))
            
            dot_products = np.dot(document_embeddings, query_embedding)
            similarities = dot_products / (doc_norms * query_norm + 1e-8)
            
            return similarities
        except Exception as e:
            print(f"Error computing similarity: {e}")
            return np.array([])
    
    def find_most_similar(self, query: str, document_texts: List[str], 
                         document_embeddings: np.ndarray = None, top_k: int = 5) -> List[tuple]:
        """
        找到与查询最相似的文档
        
        Args:
            query: 查询文本
            document_texts: 文档文本列表
            document_embeddings: 预计算的文档向量（可选）
            top_k: 返回最相似的前k个结果
            
        Returns:
            (相似度分数, 文档索引, 文档文本) 的列表
        """
        try:
            # 向量化查询
            query_embedding = self.encode_single_text(query)
            
            # 如果没有提供文档向量，则现场计算
            if document_embeddings is None:
                document_embeddings = self.encode_texts(document_texts)
            
            # 计算相似度
            similarities = self.compute_similarity(query_embedding, document_embeddings)
            
            # 获取top-k结果
            top_indices = np.argsort(similarities)[::-1][:top_k]
            
            results = []
            for idx in top_indices:
                results.append((
                    similarities[idx],
                    idx,
                    document_texts[idx] if idx < len(document_texts) else ""
                ))
            
            return results
        except Exception as e:
            print(f"Error finding similar documents: {e}")
            return []

if __name__ == "__main__":
    # 测试向量化功能
    embedding_manager = EmbeddingManager()
    
    # 测试文本
    test_texts = [
        "This is a great Chinese restaurant with delicious food",
        "Thai cuisine with spicy tom yam soup",
        "Italian pizza place with authentic flavors"
    ]
    
    # 向量化测试
    embeddings = embedding_manager.encode_texts(test_texts)
    print(f"Generated embeddings shape: {embeddings.shape}")
    
    # 相似度搜索测试
    query = "Looking for good Chinese food"
    results = embedding_manager.find_most_similar(query, test_texts, embeddings, top_k=2)
    
    print(f"\nQuery: {query}")
    print("Most similar documents:")
    for score, idx, text in results:
        print(f"Score: {score:.4f}, Index: {idx}, Text: {text}")
