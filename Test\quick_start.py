"""
快速启动脚本
一键运行RAG系统演示
"""

import os
import sys
from dotenv import load_dotenv

def main():
    """快速启动演示"""
    print("🍽️ 马来西亚餐厅RAG系统 - 快速演示")
    print("="*50)
    
    # 加载环境变量
    load_dotenv()
    
    # 检查API密钥
    api_key = os.getenv('GOOGLE_API_KEY')
    if not api_key or api_key == 'your_gemini_api_key_here':
        print("❌ 请先设置Google Gemini API密钥")
        print("1. 编辑.env文件")
        print("2. 将GOOGLE_API_KEY设置为你的实际API密钥")
        print("3. 获取API密钥: https://makersuite.google.com/app/apikey")
        return
    
    try:
        from rag_system import RAGSystem
        
        print("🚀 初始化RAG系统...")
        rag = RAGSystem()
        
        print("📚 设置数据库...")
        rag.setup_database()
        
        print("✅ 系统初始化完成！\n")
        
        # 演示查询
        demo_queries = [
            "推荐一家好的中餐厅",
            "有什么清真餐厅？",
            "吉隆坡有什么好的泰式料理？",
            "便宜又好吃的餐厅有哪些？"
        ]
        
        print("🎯 演示查询:")
        print("-" * 30)
        
        for i, query in enumerate(demo_queries, 1):
            print(f"\n{i}. 查询: {query}")
            print("   回答:", end=" ")
            
            try:
                answer = rag.generate_answer(query, top_k=2)
                # 显示回答的前150个字符
                short_answer = answer[:150] + "..." if len(answer) > 150 else answer
                print(short_answer)
            except Exception as e:
                print(f"错误: {e}")
        
        print("\n" + "="*50)
        print("🌟 演示完成！")
        print("\n📱 启动Web界面:")
        print("   streamlit run streamlit_app.py")
        print("\n🔧 运行测试:")
        print("   python test_system.py")
        print("\n💡 自定义查询:")
        print("   修改demo_queries列表添加你的问题")
        
        # 显示统计信息
        stats = rag.get_statistics()
        print(f"\n📊 数据库统计:")
        print(f"   总餐厅数: {stats.get('total_restaurants', 0)}")
        if 'cuisine_distribution' in stats:
            top_cuisines = list(stats['cuisine_distribution'].items())[:3]
            print(f"   主要菜系: {', '.join([f'{k}({v})' for k, v in top_cuisines])}")
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("请先运行: python setup.py")
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        print("请检查配置和依赖")

if __name__ == "__main__":
    main()
