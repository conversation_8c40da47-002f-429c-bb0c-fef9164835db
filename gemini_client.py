"""
Gemini API客户端模块
处理与Google Gemini API的交互
"""

import google.generativeai as genai
import os
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv

class GeminiClient:
    def __init__(self, api_key: Optional[str] = None):
        """
        初始化Gemini客户端
        
        Args:
            api_key: Google API密钥（可选，如果不提供会从环境变量读取）
        """
        # 加载环境变量
        load_dotenv()
        
        # 获取API密钥
        self.api_key = api_key or os.getenv('GOOGLE_API_KEY')
        if not self.api_key:
            raise ValueError("Google API key not found. Please set GOOGLE_API_KEY environment variable or pass api_key parameter.")
        
        # 配置Gemini
        genai.configure(api_key=self.api_key)
        
        # 初始化模型
        self.model = genai.GenerativeModel('gemini-pro')
        
        print("Gemini client initialized successfully")
    
    def generate_response(self, prompt: str, context: str = "", 
                         temperature: float = 0.7, max_tokens: int = 1000) -> str:
        """
        生成回复
        
        Args:
            prompt: 用户查询
            context: 上下文信息（从向量数据库检索的相关文档）
            temperature: 生成温度（0-1，越高越随机）
            max_tokens: 最大生成token数
            
        Returns:
            生成的回复文本
        """
        try:
            # 构建完整的提示
            full_prompt = self._build_prompt(prompt, context)
            
            # 生成配置
            generation_config = genai.types.GenerationConfig(
                temperature=temperature,
                max_output_tokens=max_tokens,
            )
            
            # 生成回复
            response = self.model.generate_content(
                full_prompt,
                generation_config=generation_config
            )
            
            return response.text
            
        except Exception as e:
            print(f"Error generating response: {e}")
            return f"抱歉，生成回复时出现错误：{str(e)}"
    
    def _build_prompt(self, user_query: str, context: str) -> str:
        """
        构建完整的提示词
        
        Args:
            user_query: 用户查询
            context: 上下文信息
            
        Returns:
            完整的提示词
        """
        prompt_template = """你是一个专业的马来西亚餐厅推荐助手。请基于以下相关餐厅信息来回答用户的问题。

相关餐厅信息：
{context}

用户问题：{user_query}

请提供详细、有用的回答，包括：
1. 直接回答用户的问题
2. 推荐合适的餐厅（如果适用）
3. 提供具体的菜品推荐
4. 包含实用信息如位置、价格范围、评分等
5. 如果有多个选择，请比较它们的优缺点

请用友好、专业的语调回答，可以使用中文、英文或马来文，根据用户问题的语言来调整。"""

        return prompt_template.format(
            context=context if context else "暂无相关餐厅信息",
            user_query=user_query
        )
    
    def generate_restaurant_summary(self, restaurant_data: Dict[str, Any]) -> str:
        """
        为餐厅数据生成摘要
        
        Args:
            restaurant_data: 餐厅数据字典
            
        Returns:
            餐厅摘要文本
        """
        try:
            prompt = f"""请为以下餐厅信息生成一个简洁的摘要：

餐厅名称：{restaurant_data.get('restaurant_name', '未知')}
菜系：{restaurant_data.get('cuisine', '未知')}
评分：{restaurant_data.get('rating', '未知')}
位置：{restaurant_data.get('location', '未知')}
优点：{restaurant_data.get('pros', '未知')}
推荐菜品：{restaurant_data.get('must_try_dishes', '未知')}

请生成一个50字以内的简洁摘要。"""

            response = self.model.generate_content(prompt)
            return response.text
            
        except Exception as e:
            print(f"Error generating summary: {e}")
            return "无法生成餐厅摘要"
    
    def chat_with_context(self, messages: List[Dict[str, str]], context: str = "") -> str:
        """
        支持多轮对话的聊天功能
        
        Args:
            messages: 对话历史，格式为 [{"role": "user", "content": "..."}, {"role": "assistant", "content": "..."}]
            context: 上下文信息
            
        Returns:
            助手的回复
        """
        try:
            # 构建对话历史
            conversation = ""
            for msg in messages:
                role = "用户" if msg["role"] == "user" else "助手"
                conversation += f"{role}: {msg['content']}\n"
            
            # 构建完整提示
            prompt = f"""你是一个专业的马来西亚餐厅推荐助手。

相关餐厅信息：
{context if context else "暂无相关餐厅信息"}

对话历史：
{conversation}

请基于上下文信息和对话历史，回答用户的最新问题。保持回答的连贯性和相关性。"""

            response = self.model.generate_content(prompt)
            return response.text
            
        except Exception as e:
            print(f"Error in chat: {e}")
            return f"抱歉，聊天时出现错误：{str(e)}"
    
    def test_connection(self) -> bool:
        """
        测试API连接
        
        Returns:
            连接是否成功
        """
        try:
            response = self.model.generate_content("Hello, this is a test.")
            return True
        except Exception as e:
            print(f"Connection test failed: {e}")
            return False

if __name__ == "__main__":
    # 测试Gemini客户端
    try:
        client = GeminiClient()
        
        # 测试连接
        if client.test_connection():
            print("✅ Gemini API connection successful")
            
            # 测试生成回复
            test_context = """
            Restaurant: Beans Cafe
            Cuisine: Western
            Rating: 4.4/5
            Location: Miri, Sarawak
            Pros: Quality coffee, air-conditioned, friendly staff
            Must-try: Kolok Mee, Coffee
            """
            
            test_query = "Tell me about a good cafe in Miri"
            response = client.generate_response(test_query, test_context)
            print(f"\nTest Query: {test_query}")
            print(f"Response: {response}")
        else:
            print("❌ Gemini API connection failed")
            
    except Exception as e:
        print(f"Error testing Gemini client: {e}")
        print("\n请确保：")
        print("1. 已设置GOOGLE_API_KEY环境变量")
        print("2. API密钥有效且有足够的配额")
        print("3. 网络连接正常")
