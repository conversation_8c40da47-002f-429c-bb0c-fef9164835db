"""
数据预处理模块
从JSONL文件中提取和处理餐厅对话数据
"""

import json
import pandas as pd
from typing import List, Dict, Any
import re

class DataProcessor:
    def __init__(self, jsonl_file_path: str):
        self.jsonl_file_path = jsonl_file_path
        self.processed_data = []
    
    def load_jsonl(self) -> List[Dict[str, Any]]:
        """加载JSONL文件"""
        data = []
        try:
            with open(self.jsonl_file_path, 'r', encoding='utf-8') as file:
                for line_num, line in enumerate(file, 1):
                    try:
                        data.append(json.loads(line.strip()))
                    except json.JSONDecodeError as e:
                        print(f"Error parsing line {line_num}: {e}")
                        continue
        except FileNotFoundError:
            print(f"File {self.jsonl_file_path} not found!")
            return []
        
        print(f"Loaded {len(data)} conversations from {self.jsonl_file_path}")
        return data
    
    def extract_restaurant_info(self, conversation: Dict[str, Any]) -> Dict[str, str]:
        """从对话中提取餐厅信息"""
        user_query = ""
        model_response = ""
        
        if "contents" in conversation:
            for content in conversation["contents"]:
                if content["role"] == "user":
                    user_query = content["parts"][0]["text"]
                elif content["role"] == "model":
                    model_response = content["parts"][0]["text"]
        
        # 从模型回复中提取结构化信息
        restaurant_info = self.parse_restaurant_response(model_response)
        restaurant_info["user_query"] = user_query
        restaurant_info["full_response"] = model_response
        
        return restaurant_info
    
    def parse_restaurant_response(self, response: str) -> Dict[str, str]:
        """解析餐厅回复，提取结构化信息"""
        info = {
            "restaurant_name": "",
            "cuisine": "",
            "rating": "",
            "price_range": "",
            "location": "",
            "halal_status": "",
            "pros": "",
            "cons": "",
            "must_try_dishes": ""
        }
        
        # 提取餐厅名称（通常在开头）
        name_match = re.search(r'^.*?([A-Za-z\s\u4e00-\u9fff\u0100-\u017f]+(?:Restaurant|Cafe|Kitchen|Corner|Bar|Restoran|Kedai|Warong)[A-Za-z\s\u4e00-\u9fff\u0100-\u017f]*)', response)
        if name_match:
            info["restaurant_name"] = name_match.group(1).strip()
        
        # 提取菜系
        cuisine_match = re.search(r'\*\*Cuisine:\*\*\s*([^\|]+)', response)
        if cuisine_match:
            info["cuisine"] = cuisine_match.group(1).strip()
        
        # 提取评分
        rating_match = re.search(r'\*\*Rating:\*\*\s*([^\|]+)', response)
        if rating_match:
            info["rating"] = rating_match.group(1).strip()
        
        # 提取价格范围
        price_match = re.search(r'\*\*Price Range:\*\*\s*([^\|]+)', response)
        if price_match:
            info["price_range"] = price_match.group(1).strip()
        
        # 提取位置
        location_match = re.search(r'\*\*Location:\*\*\s*([^\n]+)', response)
        if location_match:
            info["location"] = location_match.group(1).strip()
        
        # 提取清真状态
        halal_match = re.search(r'\*\*Halal Status:\*\*\s*([^\|]+)', response)
        if halal_match:
            info["halal_status"] = halal_match.group(1).strip()
        
        # 提取优点
        pros_match = re.search(r'👍\s*\*\*What\'s Great:\*\*\s*\n(.*?)(?=👎|\n\n🍛)', response, re.DOTALL)
        if pros_match:
            info["pros"] = pros_match.group(1).strip()
        
        # 提取缺点
        cons_match = re.search(r'👎\s*\*\*Things to Note:\*\*\s*\n(.*?)(?=🍛|\n\n)', response, re.DOTALL)
        if cons_match:
            info["cons"] = cons_match.group(1).strip()
        
        # 提取推荐菜品
        dishes_match = re.search(r'🍛\s*\*\*Must-Try Dishes:\*\*\s*\n(.*?)(?=\n\n|$)', response, re.DOTALL)
        if dishes_match:
            info["must_try_dishes"] = dishes_match.group(1).strip()
        
        return info
    
    def process_all_data(self) -> pd.DataFrame:
        """处理所有数据并返回DataFrame"""
        raw_data = self.load_jsonl()
        
        for conversation in raw_data:
            restaurant_info = self.extract_restaurant_info(conversation)
            self.processed_data.append(restaurant_info)
        
        df = pd.DataFrame(self.processed_data)
        print(f"Processed {len(df)} restaurant entries")
        return df
    
    def create_text_for_embedding(self, row: pd.Series) -> str:
        """为每个餐厅创建用于向量化的文本"""
        text_parts = []
        
        if row['restaurant_name']:
            text_parts.append(f"Restaurant: {row['restaurant_name']}")
        
        if row['cuisine']:
            text_parts.append(f"Cuisine: {row['cuisine']}")
        
        if row['location']:
            text_parts.append(f"Location: {row['location']}")
        
        if row['pros']:
            text_parts.append(f"Pros: {row['pros']}")
        
        if row['must_try_dishes']:
            text_parts.append(f"Must-try dishes: {row['must_try_dishes']}")
        
        if row['user_query']:
            text_parts.append(f"Common queries: {row['user_query']}")
        
        return " | ".join(text_parts)

if __name__ == "__main__":
    # 测试数据处理
    processor = DataProcessor("vertex_ai_training_data.jsonl")
    df = processor.process_all_data()
    
    # 显示前几条数据
    print("\nFirst 3 processed entries:")
    for i in range(min(3, len(df))):
        print(f"\n--- Entry {i+1} ---")
        print(f"Restaurant: {df.iloc[i]['restaurant_name']}")
        print(f"Cuisine: {df.iloc[i]['cuisine']}")
        print(f"Rating: {df.iloc[i]['rating']}")
        print(f"Location: {df.iloc[i]['location']}")
