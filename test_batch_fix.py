"""
测试批处理修复
"""

import os
from dotenv import load_dotenv

def test_batch_processing():
    """测试批处理功能"""
    print("🔍 测试批处理修复...")
    
    try:
        from rag_system import RAGSystem
        
        # 初始化RAG系统
        print("初始化RAG系统...")
        rag = RAGSystem()
        
        # 强制重建数据库以测试批处理
        print("开始批处理测试（这可能需要几分钟）...")
        rag.setup_database(force_rebuild=True)
        
        # 检查数据库状态
        stats = rag.get_statistics()
        total_restaurants = stats.get('total_restaurants', 0)
        
        if total_restaurants > 0:
            print(f"✅ 批处理成功！数据库包含 {total_restaurants} 条餐厅数据")
            
            # 测试搜索功能
            print("测试搜索功能...")
            results = rag.search_restaurants("中餐厅", top_k=3)
            
            if results:
                print(f"✅ 搜索成功，找到 {len(results)} 个结果")
                for i, result in enumerate(results[:2], 1):
                    restaurant_name = result['metadata'].get('restaurant_name', '未知')
                    print(f"  {i}. {restaurant_name}")
                
                # 测试问答功能
                print("测试问答功能...")
                answer = rag.generate_answer("推荐一家好的中餐厅")
                
                if answer and len(answer) > 20:
                    print("✅ 问答功能正常")
                    print(f"回答示例: {answer[:100]}...")
                    return True
                else:
                    print("❌ 问答功能异常")
                    return False
            else:
                print("❌ 搜索功能异常")
                return False
        else:
            print("❌ 数据库为空，批处理可能失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def quick_test():
    """快速测试（使用现有数据库）"""
    print("🚀 快速功能测试...")
    
    try:
        from rag_system import RAGSystem
        
        rag = RAGSystem()
        
        # 不重建，使用现有数据库
        rag.setup_database(force_rebuild=False)
        
        stats = rag.get_statistics()
        total_restaurants = stats.get('total_restaurants', 0)
        
        print(f"数据库状态: {total_restaurants} 条餐厅数据")
        
        if total_restaurants > 0:
            # 测试几个查询
            test_queries = [
                "推荐一家中餐厅",
                "有什么清真餐厅？",
                "吉隆坡有什么好吃的？"
            ]
            
            print("测试查询:")
            for i, query in enumerate(test_queries, 1):
                print(f"\n{i}. 查询: {query}")
                try:
                    answer = rag.generate_answer(query, top_k=2)
                    short_answer = answer[:100] + "..." if len(answer) > 100 else answer
                    print(f"   回答: {short_answer}")
                except Exception as e:
                    print(f"   错误: {e}")
            
            return True
        else:
            print("数据库为空，需要先运行完整测试")
            return False
            
    except Exception as e:
        print(f"❌ 快速测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 RAG系统批处理修复测试\n")
    
    # 加载环境变量
    load_dotenv()
    
    # 检查API密钥
    api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')
    if not api_key:
        print("❌ 未找到API密钥，请设置环境变量")
        return
    
    print("选择测试模式:")
    print("1. 快速测试（使用现有数据库）")
    print("2. 完整测试（重建数据库，需要15-20分钟）")
    
    choice = input("\n请选择 (1/2): ").strip()
    
    if choice == "1":
        success = quick_test()
    elif choice == "2":
        success = test_batch_processing()
    else:
        print("无效选择")
        return
    
    print("\n" + "="*50)
    if success:
        print("🎉 测试成功！RAG系统正常工作")
        print("\n可以运行:")
        print("- streamlit run streamlit_app.py  # Web界面")
        print("- python quick_start.py          # 快速演示")
    else:
        print("❌ 测试失败，请检查错误信息")
    print("="*50)

if __name__ == "__main__":
    main()
