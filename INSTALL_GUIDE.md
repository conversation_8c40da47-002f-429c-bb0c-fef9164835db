# 🚀 RAG系统安装指南 - 官方API版本

## 📋 更新说明

项目已更新为使用Google官方的Gemini API接口，与官方文档保持一致。

### 🔄 主要变化

1. **API包更新**: `google-generativeai` → `google-genai`
2. **导入方式**: `import google.generativeai as genai` → `from google import genai`
3. **客户端初始化**: 使用官方的 `genai.Client()`
4. **环境变量**: 支持 `GEMINI_API_KEY` 和 `GOOGLE_API_KEY`
5. **模型名称**: 默认使用 `gemini-2.0-flash-exp`

## 🛠️ 安装步骤

### 1. 安装依赖

```bash
# 方法1: 使用requirements.txt
pip install -r requirements.txt

# 方法2: 手动安装核心包
pip install google-genai sentence-transformers chromadb streamlit pandas "numpy<2.0.0" python-dotenv plotly

# 方法3: 运行修复脚本
python fix_dependencies.py
```

### 2. 设置API密钥

#### 获取API密钥
1. 访问 [Google AI Studio](https://aistudio.google.com/app/apikey)
2. 登录Google账户
3. 创建新的API密钥
4. 复制密钥

#### 配置环境变量
创建 `.env` 文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，添加API密钥：
```env
# 推荐使用官方环境变量名
GEMINI_API_KEY=your_actual_api_key_here

# 或者使用旧的环境变量名（向后兼容）
# GOOGLE_API_KEY=your_actual_api_key_here
```

### 3. 测试安装

```bash
# 测试Gemini API
python test_gemini_api.py

# 测试完整系统
python test_system.py
```

### 4. 运行系统

```bash
# Web界面
streamlit run streamlit_app.py

# 命令行版本
python rag_system.py

# 快速演示
python quick_start.py
```

## 🔧 故障排除

### 常见问题

#### 1. 导入错误
```
ModuleNotFoundError: No module named 'google.genai'
```
**解决方案:**
```bash
pip install google-genai
```

#### 2. API密钥错误
```
ValueError: Google API key not found
```
**解决方案:**
- 检查 `.env` 文件是否存在
- 确认API密钥格式正确
- 尝试两种环境变量名称

#### 3. 模型不可用
```
Error: Model not found
```
**解决方案:**
- 运行 `python test_gemini_api.py` 查看可用模型
- 在代码中指定其他模型名称

#### 4. 网络连接问题
**解决方案:**
- 检查网络连接
- 确认防火墙设置
- 尝试使用VPN

### 兼容性说明

- **Python版本**: 3.8+
- **操作系统**: Windows, macOS, Linux
- **依赖冲突**: 如遇到numpy版本冲突，使用 `numpy<2.0.0`

## 📊 API使用示例

### 基本调用
```python
from google import genai
import os

# 设置API密钥
os.environ['GEMINI_API_KEY'] = 'your_api_key'

# 创建客户端
client = genai.Client()

# 生成内容
response = client.models.generate_content(
    model="gemini-2.0-flash-exp",
    contents="解释人工智能"
)

print(response.text)
```

### 在RAG系统中使用
```python
from gemini_client import GeminiClient

# 初始化客户端
client = GeminiClient()

# 生成回复
answer = client.generate_response(
    prompt="推荐一家中餐厅",
    context="相关餐厅信息..."
)
```

## 🎯 下一步

1. **测试系统**: 运行所有测试确保正常工作
2. **导入数据**: 确保 `vertex_ai_training_data.jsonl` 文件存在
3. **初始化数据库**: 运行 `rag.setup_database()` 
4. **开始使用**: 通过Web界面或API进行查询

## 📞 支持

如果遇到问题：
1. 查看错误日志
2. 运行测试脚本诊断
3. 检查API配额和权限
4. 参考官方文档: https://ai.google.dev/

---

**注意**: 确保API密钥安全，不要提交到版本控制系统中。
