"""
RAG系统测试脚本
用于测试各个组件的功能
"""

import os
import sys
from dotenv import load_dotenv

def test_data_processor():
    """测试数据处理模块"""
    print("🔍 测试数据处理模块...")
    try:
        from data_processor import DataProcessor
        
        processor = DataProcessor("vertex_ai_training_data.jsonl")
        df = processor.process_all_data()
        
        if not df.empty:
            print(f"✅ 数据处理成功，共处理 {len(df)} 条餐厅数据")
            print(f"   示例餐厅: {df.iloc[0]['restaurant_name']}")
            return True
        else:
            print("❌ 数据处理失败，没有数据")
            return False
    except Exception as e:
        print(f"❌ 数据处理模块测试失败: {e}")
        return False

def test_embedding_manager():
    """测试向量化模块"""
    print("\n🔍 测试向量化模块...")
    try:
        from embedding_manager import EmbeddingManager
        
        embedding_manager = EmbeddingManager("all-MiniLM-L6-v2")
        
        # 测试文本
        test_texts = [
            "Great Chinese restaurant with delicious food",
            "Thai cuisine with spicy tom yam soup"
        ]
        
        embeddings = embedding_manager.encode_texts(test_texts)
        
        if embeddings.size > 0:
            print(f"✅ 向量化成功，生成向量维度: {embeddings.shape}")
            return True
        else:
            print("❌ 向量化失败")
            return False
    except Exception as e:
        print(f"❌ 向量化模块测试失败: {e}")
        return False

def test_vector_database():
    """测试向量数据库模块"""
    print("\n🔍 测试向量数据库模块...")
    try:
        from vector_database import VectorDatabase
        
        db = VectorDatabase(db_path="./test_chroma_db")
        
        # 测试数据
        test_documents = ["Test restaurant with good food"]
        test_metadatas = [{"restaurant_name": "Test Restaurant", "cuisine": "Test"}]
        
        db.add_documents(test_documents, test_metadatas)
        
        # 测试搜索
        results = db.search("good food", n_results=1)
        
        if results and 'documents' in results:
            print("✅ 向量数据库测试成功")
            # 清理测试数据库
            db.delete_collection()
            return True
        else:
            print("❌ 向量数据库测试失败")
            return False
    except Exception as e:
        print(f"❌ 向量数据库模块测试失败: {e}")
        return False

def test_gemini_client():
    """测试Gemini API客户端"""
    print("\n🔍 测试Gemini API客户端...")
    
    # 检查API密钥
    api_key = os.getenv('GOOGLE_API_KEY')
    if not api_key:
        print("❌ 未找到GOOGLE_API_KEY环境变量")
        print("   请在.env文件中设置API密钥")
        return False
    
    try:
        from gemini_client import GeminiClient
        
        client = GeminiClient()
        
        if client.test_connection():
            print("✅ Gemini API连接成功")
            
            # 测试生成回复
            test_response = client.generate_response(
                "Tell me about a restaurant",
                "Test restaurant: Good Chinese food"
            )
            
            if test_response and len(test_response) > 10:
                print("✅ Gemini API回复生成成功")
                return True
            else:
                print("❌ Gemini API回复生成失败")
                return False
        else:
            print("❌ Gemini API连接失败")
            return False
    except Exception as e:
        print(f"❌ Gemini API客户端测试失败: {e}")
        return False

def test_rag_system():
    """测试完整RAG系统"""
    print("\n🔍 测试完整RAG系统...")
    try:
        from rag_system import RAGSystem
        
        rag = RAGSystem()
        
        # 设置数据库（小规模测试）
        print("   正在设置数据库...")
        rag.setup_database()
        
        # 测试搜索
        results = rag.search_restaurants("Chinese restaurant", top_k=3)
        
        if results:
            print(f"✅ RAG搜索成功，找到 {len(results)} 个结果")
            
            # 测试问答
            answer = rag.generate_answer("推荐一家中餐厅")
            
            if answer and len(answer) > 20:
                print("✅ RAG问答成功")
                print(f"   示例回答: {answer[:100]}...")
                return True
            else:
                print("❌ RAG问答失败")
                return False
        else:
            print("❌ RAG搜索失败")
            return False
    except Exception as e:
        print(f"❌ RAG系统测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试RAG系统各个组件...\n")
    
    # 加载环境变量
    load_dotenv()
    
    # 检查必要文件
    if not os.path.exists("vertex_ai_training_data.jsonl"):
        print("❌ 未找到数据文件 vertex_ai_training_data.jsonl")
        print("   请确保数据文件在当前目录下")
        return
    
    # 测试各个组件
    tests = [
        ("数据处理", test_data_processor),
        ("向量化", test_embedding_manager),
        ("向量数据库", test_vector_database),
        ("Gemini API", test_gemini_client),
        ("完整RAG系统", test_rag_system)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        results[test_name] = test_func()
    
    # 显示测试结果
    print("\n" + "="*50)
    print("📊 测试结果汇总:")
    print("="*50)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name:15} : {status}")
        if not passed:
            all_passed = False
    
    print("="*50)
    
    if all_passed:
        print("🎉 所有测试通过！系统可以正常使用")
        print("\n📝 下一步:")
        print("1. 运行 'streamlit run streamlit_app.py' 启动Web界面")
        print("2. 或者直接使用 'python rag_system.py' 进行命令行测试")
    else:
        print("⚠️  部分测试失败，请检查配置和依赖")
        print("\n🔧 故障排除:")
        print("1. 确保已安装所有依赖: pip install -r requirements.txt")
        print("2. 检查.env文件中的GOOGLE_API_KEY")
        print("3. 确保vertex_ai_training_data.jsonl文件存在")
        print("4. 检查网络连接")

if __name__ == "__main__":
    main()
