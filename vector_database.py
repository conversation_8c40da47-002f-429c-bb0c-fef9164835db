"""
向量数据库管理模块
使用ChromaDB存储和检索向量化数据
"""

import chromadb
from chromadb.config import Settings
import pandas as pd
from typing import List, Dict, Any, Optional
import uuid
import os

class VectorDatabase:
    def __init__(self, db_path: str = "./chroma_db", collection_name: str = "restaurant_data"):
        """
        初始化向量数据库
        
        Args:
            db_path: 数据库存储路径
            collection_name: 集合名称
        """
        self.db_path = db_path
        self.collection_name = collection_name
        self.client = None
        self.collection = None
        self.initialize_database()
    
    def initialize_database(self):
        """初始化ChromaDB客户端和集合"""
        try:
            # 创建数据库目录
            os.makedirs(self.db_path, exist_ok=True)
            
            # 初始化ChromaDB客户端
            self.client = chromadb.PersistentClient(
                path=self.db_path,
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            
            # 获取或创建集合
            try:
                self.collection = self.client.get_collection(name=self.collection_name)
                print(f"Loaded existing collection: {self.collection_name}")
            except:
                self.collection = self.client.create_collection(
                    name=self.collection_name,
                    metadata={"description": "Restaurant information for RAG system"}
                )
                print(f"Created new collection: {self.collection_name}")
                
        except Exception as e:
            print(f"Error initializing database: {e}")
    
    def add_documents(self, documents: List[str], metadatas: List[Dict[str, Any]],
                     embeddings: Optional[List[List[float]]] = None, ids: Optional[List[str]] = None,
                     batch_size: int = 5000):
        """
        添加文档到向量数据库（支持批处理）

        Args:
            documents: 文档文本列表
            metadatas: 文档元数据列表
            embeddings: 预计算的向量（可选，如果不提供会自动计算）
            ids: 文档ID列表（可选，如果不提供会自动生成）
            batch_size: 批处理大小，默认5000
        """
        try:
            if not documents:
                print("No documents to add")
                return

            # 生成ID（如果未提供）
            if ids is None:
                ids = [str(uuid.uuid4()) for _ in range(len(documents))]

            # 确保所有列表长度一致
            assert len(documents) == len(metadatas), "Documents and metadatas must have same length"
            if embeddings:
                assert len(documents) == len(embeddings), "Documents and embeddings must have same length"

            # 分批处理
            total_docs = len(documents)
            added_count = 0

            for i in range(0, total_docs, batch_size):
                end_idx = min(i + batch_size, total_docs)

                batch_documents = documents[i:end_idx]
                batch_metadatas = metadatas[i:end_idx]
                batch_ids = ids[i:end_idx]

                print(f"Adding batch {i//batch_size + 1}: documents {i+1}-{end_idx} of {total_docs}")

                # 添加到集合
                if embeddings:
                    batch_embeddings = embeddings[i:end_idx]
                    self.collection.add(
                        documents=batch_documents,
                        metadatas=batch_metadatas,
                        embeddings=batch_embeddings,
                        ids=batch_ids
                    )
                else:
                    self.collection.add(
                        documents=batch_documents,
                        metadatas=batch_metadatas,
                        ids=batch_ids
                    )

                added_count += len(batch_documents)
                print(f"Successfully added {len(batch_documents)} documents (Total: {added_count}/{total_docs})")

            print(f"✅ Successfully added all {added_count} documents to collection")

        except Exception as e:
            print(f"❌ Error adding documents: {e}")
            raise
    
    def search(self, query: str, n_results: int = 5, where: Optional[Dict] = None) -> Dict[str, Any]:
        """
        搜索相似文档
        
        Args:
            query: 查询文本
            n_results: 返回结果数量
            where: 过滤条件（可选）
            
        Returns:
            搜索结果字典
        """
        try:
            results = self.collection.query(
                query_texts=[query],
                n_results=n_results,
                where=where
            )
            return results
        except Exception as e:
            print(f"Error searching: {e}")
            return {}
    
    def search_with_embeddings(self, query_embeddings: List[List[float]], 
                              n_results: int = 5, where: Optional[Dict] = None) -> Dict[str, Any]:
        """
        使用预计算的向量进行搜索
        
        Args:
            query_embeddings: 查询向量
            n_results: 返回结果数量
            where: 过滤条件（可选）
            
        Returns:
            搜索结果字典
        """
        try:
            results = self.collection.query(
                query_embeddings=query_embeddings,
                n_results=n_results,
                where=where
            )
            return results
        except Exception as e:
            print(f"Error searching with embeddings: {e}")
            return {}
    
    def get_collection_info(self) -> Dict[str, Any]:
        """获取集合信息"""
        try:
            count = self.collection.count()
            return {
                "name": self.collection_name,
                "count": count,
                "metadata": self.collection.metadata
            }
        except Exception as e:
            print(f"Error getting collection info: {e}")
            return {}
    
    def delete_collection(self):
        """删除集合"""
        try:
            self.client.delete_collection(name=self.collection_name)
            print(f"Deleted collection: {self.collection_name}")
        except Exception as e:
            print(f"Error deleting collection: {e}")
    
    def reset_collection(self):
        """重置集合（删除所有数据）"""
        try:
            self.delete_collection()
            self.collection = self.client.create_collection(
                name=self.collection_name,
                metadata={"description": "Restaurant information for RAG system"}
            )
            print(f"Reset collection: {self.collection_name}")
        except Exception as e:
            print(f"Error resetting collection: {e}")
    
    def update_document(self, doc_id: str, document: str = None, 
                       metadata: Dict[str, Any] = None, embedding: List[float] = None):
        """
        更新文档
        
        Args:
            doc_id: 文档ID
            document: 新的文档文本（可选）
            metadata: 新的元数据（可选）
            embedding: 新的向量（可选）
        """
        try:
            update_data = {"ids": [doc_id]}
            
            if document is not None:
                update_data["documents"] = [document]
            if metadata is not None:
                update_data["metadatas"] = [metadata]
            if embedding is not None:
                update_data["embeddings"] = [embedding]
            
            self.collection.update(**update_data)
            print(f"Updated document: {doc_id}")
            
        except Exception as e:
            print(f"Error updating document: {e}")
    
    def delete_document(self, doc_id: str):
        """删除文档"""
        try:
            self.collection.delete(ids=[doc_id])
            print(f"Deleted document: {doc_id}")
        except Exception as e:
            print(f"Error deleting document: {e}")
    
    def get_document(self, doc_id: str) -> Dict[str, Any]:
        """获取特定文档"""
        try:
            result = self.collection.get(ids=[doc_id])
            return result
        except Exception as e:
            print(f"Error getting document: {e}")
            return {}

if __name__ == "__main__":
    # 测试向量数据库功能
    db = VectorDatabase()
    
    # 测试数据
    test_documents = [
        "Great Chinese restaurant with authentic Beijing duck",
        "Spicy Thai food with excellent tom yam soup",
        "Italian pizzeria with wood-fired oven"
    ]
    
    test_metadatas = [
        {"restaurant_name": "Beijing House", "cuisine": "Chinese", "rating": "4.5"},
        {"restaurant_name": "Thai Spice", "cuisine": "Thai", "rating": "4.2"},
        {"restaurant_name": "Mario's Pizza", "cuisine": "Italian", "rating": "4.0"}
    ]
    
    # 添加测试数据
    db.add_documents(test_documents, test_metadatas)
    
    # 搜索测试
    results = db.search("Looking for good Chinese food", n_results=2)
    print("Search results:")
    print(results)
    
    # 获取集合信息
    info = db.get_collection_info()
    print(f"Collection info: {info}")
