"""
RAG (Retrieval-Augmented Generation) 系统
整合数据处理、向量化、向量数据库和Gemini API
"""

import pandas as pd
from typing import List, Dict, Any, Optional
import json
import os
from datetime import datetime

from data_processor import DataProcessor
from embedding_manager import EmbeddingManager
from vector_database import VectorDatabase
from gemini_client import GeminiClient

class RAGSystem:
    def __init__(self, 
                 jsonl_file_path: str = "vertex_ai_training_data.jsonl",
                 db_path: str = "./chroma_db",
                 embedding_model: str = "paraphrase-multilingual-MiniLM-L12-v2"):
        """
        初始化RAG系统
        
        Args:
            jsonl_file_path: JSONL数据文件路径
            db_path: 向量数据库路径
            embedding_model: 向量化模型名称
        """
        self.jsonl_file_path = jsonl_file_path
        self.db_path = db_path
        self.embedding_model = embedding_model
        
        # 初始化组件
        self.data_processor = DataProcessor(jsonl_file_path)
        self.embedding_manager = EmbeddingManager(embedding_model)
        self.vector_db = VectorDatabase(db_path)
        self.gemini_client = GeminiClient()
        
        # 数据存储
        self.restaurant_data = None
        self.is_initialized = False
        
        print("RAG System initialized successfully")
    
    def setup_database(self, force_rebuild: bool = False):
        """
        设置数据库（处理数据、向量化、存储）
        
        Args:
            force_rebuild: 是否强制重建数据库
        """
        try:
            # 检查是否需要重建
            collection_info = self.vector_db.get_collection_info()
            if collection_info.get('count', 0) > 0 and not force_rebuild:
                print(f"Database already contains {collection_info['count']} documents. Use force_rebuild=True to rebuild.")
                self.is_initialized = True
                return
            
            print("Setting up database...")
            
            # 1. 处理数据
            print("Step 1: Processing data...")
            self.restaurant_data = self.data_processor.process_all_data()
            
            if self.restaurant_data.empty:
                print("No data to process!")
                return
            
            # 2. 准备文档和元数据
            print("Step 2: Preparing documents...")
            documents = []
            metadatas = []
            
            for idx, row in self.restaurant_data.iterrows():
                # 创建用于向量化的文本
                doc_text = self.data_processor.create_text_for_embedding(row)
                documents.append(doc_text)
                
                # 创建元数据
                metadata = {
                    "restaurant_name": row.get('restaurant_name', ''),
                    "cuisine": row.get('cuisine', ''),
                    "rating": row.get('rating', ''),
                    "location": row.get('location', ''),
                    "price_range": row.get('price_range', ''),
                    "halal_status": row.get('halal_status', ''),
                    "pros": row.get('pros', ''),
                    "cons": row.get('cons', ''),
                    "must_try_dishes": row.get('must_try_dishes', ''),
                    "full_response": row.get('full_response', ''),
                    "user_query": row.get('user_query', ''),
                    "doc_id": str(idx)
                }
                metadatas.append(metadata)
            
            # 3. 向量化文档
            print("Step 3: Generating embeddings...")
            embeddings = self.embedding_manager.encode_texts(documents)
            
            # 4. 存储到向量数据库
            print("Step 4: Storing in vector database...")
            if force_rebuild:
                self.vector_db.reset_collection()
            
            self.vector_db.add_documents(
                documents=documents,
                metadatas=metadatas,
                embeddings=embeddings.tolist()
            )
            
            self.is_initialized = True
            print("✅ Database setup completed successfully!")
            
            # 显示统计信息
            info = self.vector_db.get_collection_info()
            print(f"📊 Database contains {info['count']} restaurant entries")
            
        except Exception as e:
            print(f"❌ Error setting up database: {e}")
    
    def search_restaurants(self, query: str, top_k: int = 5, 
                          cuisine_filter: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        搜索相关餐厅
        
        Args:
            query: 搜索查询
            top_k: 返回结果数量
            cuisine_filter: 菜系过滤（可选）
            
        Returns:
            搜索结果列表
        """
        try:
            if not self.is_initialized:
                print("Database not initialized. Please run setup_database() first.")
                return []
            
            # 构建过滤条件
            where_filter = None
            if cuisine_filter:
                where_filter = {"cuisine": {"$eq": cuisine_filter}}
            
            # 搜索
            results = self.vector_db.search(
                query=query,
                n_results=top_k,
                where=where_filter
            )
            
            # 格式化结果
            formatted_results = []
            if results and 'documents' in results:
                for i in range(len(results['documents'][0])):
                    result = {
                        'document': results['documents'][0][i],
                        'metadata': results['metadatas'][0][i],
                        'distance': results['distances'][0][i] if 'distances' in results else None,
                        'id': results['ids'][0][i]
                    }
                    formatted_results.append(result)
            
            return formatted_results
            
        except Exception as e:
            print(f"Error searching restaurants: {e}")
            return []
    
    def generate_answer(self, query: str, top_k: int = 3, 
                       cuisine_filter: Optional[str] = None) -> str:
        """
        生成基于RAG的回答
        
        Args:
            query: 用户查询
            top_k: 检索的文档数量
            cuisine_filter: 菜系过滤
            
        Returns:
            生成的回答
        """
        try:
            # 1. 检索相关文档
            search_results = self.search_restaurants(query, top_k, cuisine_filter)
            
            if not search_results:
                return "抱歉，我没有找到相关的餐厅信息。请尝试其他关键词或检查数据库是否已正确设置。"
            
            # 2. 构建上下文
            context_parts = []
            for i, result in enumerate(search_results, 1):
                metadata = result['metadata']
                context_part = f"""
餐厅 {i}:
- 名称: {metadata.get('restaurant_name', '未知')}
- 菜系: {metadata.get('cuisine', '未知')}
- 评分: {metadata.get('rating', '未知')}
- 位置: {metadata.get('location', '未知')}
- 价格: {metadata.get('price_range', '未知')}
- 清真: {metadata.get('halal_status', '未知')}
- 优点: {metadata.get('pros', '未知')}
- 缺点: {metadata.get('cons', '未知')}
- 推荐菜品: {metadata.get('must_try_dishes', '未知')}
"""
                context_parts.append(context_part)
            
            context = "\n".join(context_parts)
            
            # 3. 生成回答
            answer = self.gemini_client.generate_response(query, context)
            
            return answer
            
        except Exception as e:
            print(f"Error generating answer: {e}")
            return f"抱歉，生成回答时出现错误：{str(e)}"
    
    def chat(self, query: str, conversation_history: List[Dict[str, str]] = None) -> str:
        """
        聊天功能（支持对话历史）
        
        Args:
            query: 用户查询
            conversation_history: 对话历史
            
        Returns:
            助手回复
        """
        try:
            # 检索相关信息
            search_results = self.search_restaurants(query, top_k=3)
            
            # 构建上下文
            context = ""
            if search_results:
                context_parts = []
                for result in search_results:
                    metadata = result['metadata']
                    context_part = f"{metadata.get('restaurant_name', '未知')} - {metadata.get('cuisine', '未知')} - {metadata.get('rating', '未知')}"
                    context_parts.append(context_part)
                context = "; ".join(context_parts)
            
            # 准备对话历史
            if conversation_history is None:
                conversation_history = []
            
            # 添加当前查询
            messages = conversation_history + [{"role": "user", "content": query}]
            
            # 生成回复
            response = self.gemini_client.chat_with_context(messages, context)
            
            return response
            
        except Exception as e:
            print(f"Error in chat: {e}")
            return f"抱歉，聊天时出现错误：{str(e)}"
    
    def get_restaurant_by_name(self, restaurant_name: str) -> Optional[Dict[str, Any]]:
        """
        根据餐厅名称获取详细信息
        
        Args:
            restaurant_name: 餐厅名称
            
        Returns:
            餐厅信息字典
        """
        try:
            results = self.vector_db.search(
                query=restaurant_name,
                n_results=1,
                where={"restaurant_name": {"$eq": restaurant_name}}
            )
            
            if results and results['metadatas'] and results['metadatas'][0]:
                return results['metadatas'][0][0]
            
            return None
            
        except Exception as e:
            print(f"Error getting restaurant by name: {e}")
            return None
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            info = self.vector_db.get_collection_info()
            
            # 如果有餐厅数据，计算更多统计信息
            stats = {
                "total_restaurants": info.get('count', 0),
                "database_path": self.db_path,
                "embedding_model": self.embedding_model
            }
            
            if self.restaurant_data is not None:
                # 菜系统计
                cuisine_counts = self.restaurant_data['cuisine'].value_counts().to_dict()
                stats["cuisine_distribution"] = cuisine_counts
                
                # 评分统计
                ratings = self.restaurant_data['rating'].dropna()
                if not ratings.empty:
                    stats["average_rating"] = ratings.str.extract(r'(\d+\.?\d*)').astype(float).mean().iloc[0]
            
            return stats
            
        except Exception as e:
            print(f"Error getting statistics: {e}")
            return {}

if __name__ == "__main__":
    # 测试RAG系统
    print("🚀 Initializing RAG System...")
    rag = RAGSystem()
    
    # 设置数据库
    print("\n📚 Setting up database...")
    rag.setup_database()
    
    # 测试搜索
    print("\n🔍 Testing search...")
    test_query = "推荐一家好的中餐厅"
    answer = rag.generate_answer(test_query)
    print(f"Query: {test_query}")
    print(f"Answer: {answer}")
    
    # 显示统计信息
    print("\n📊 Database Statistics:")
    stats = rag.get_statistics()
    for key, value in stats.items():
        print(f"  {key}: {value}")
