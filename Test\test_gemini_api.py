"""
测试更新后的Gemini API客户端
"""

import os
from dotenv import load_dotenv

def test_official_api():
    """测试官方API调用方式"""
    print("🔍 测试官方Gemini API...")
    
    # 加载环境变量
    load_dotenv()
    
    # 检查API密钥
    api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')
    if not api_key:
        print("❌ 未找到API密钥")
        print("请在.env文件中设置 GEMINI_API_KEY 或 GOOGLE_API_KEY")
        return False
    
    try:
        # 使用官方API方式
        from google import genai
        
        # 设置环境变量
        os.environ['GEMINI_API_KEY'] = api_key
        
        # 创建客户端
        client = genai.Client()
        
        # 测试API调用
        response = client.models.generate_content(
            model="gemini-2.0-flash-exp",
            contents="用一句话介绍人工智能"
        )
        
        print("✅ 官方API测试成功")
        print(f"回复: {response.text}")
        return True
        
    except Exception as e:
        print(f"❌ 官方API测试失败: {e}")
        return False

def test_updated_client():
    """测试更新后的客户端"""
    print("\n🔍 测试更新后的GeminiClient...")
    
    try:
        from gemini_client import GeminiClient
        
        # 初始化客户端
        client = GeminiClient()
        
        # 测试连接
        if client.test_connection():
            print("✅ 客户端连接成功")
            
            # 测试生成回复
            test_context = """
            Restaurant: Test Restaurant
            Cuisine: Chinese
            Rating: 4.5/5
            Location: Kuala Lumpur
            Pros: Great food, friendly service
            Must-try: Fried rice, Sweet and sour pork
            """
            
            response = client.generate_response(
                "推荐一家中餐厅",
                test_context
            )
            
            print("✅ 回复生成成功")
            print(f"回复: {response[:100]}...")
            return True
        else:
            print("❌ 客户端连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 客户端测试失败: {e}")
        return False

def test_available_models():
    """测试可用的模型"""
    print("\n🔍 测试可用模型...")
    
    try:
        from google import genai
        
        # 设置API密钥
        api_key = os.getenv('GEMINI_API_KEY') or os.getenv('GOOGLE_API_KEY')
        os.environ['GEMINI_API_KEY'] = api_key
        
        client = genai.Client()
        
        # 测试不同模型
        models_to_test = [
            "gemini-2.0-flash-exp",
            "gemini-1.5-flash",
            "gemini-1.5-pro"
        ]
        
        working_models = []
        
        for model in models_to_test:
            try:
                response = client.models.generate_content(
                    model=model,
                    contents="Hello"
                )
                if response and response.text:
                    working_models.append(model)
                    print(f"✅ {model} - 可用")
                else:
                    print(f"❌ {model} - 无响应")
            except Exception as e:
                print(f"❌ {model} - 错误: {str(e)[:50]}...")
        
        print(f"\n可用模型: {working_models}")
        return len(working_models) > 0
        
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试更新后的Gemini API实现...\n")
    
    # 加载环境变量
    load_dotenv()
    
    tests = [
        ("官方API调用", test_official_api),
        ("更新后的客户端", test_updated_client),
        ("可用模型测试", test_available_models)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        results[test_name] = test_func()
    
    # 显示结果
    print("\n" + "="*50)
    print("📊 测试结果汇总:")
    print("="*50)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name:20} : {status}")
        if not passed:
            all_passed = False
    
    print("="*50)
    
    if all_passed:
        print("🎉 所有测试通过！API更新成功")
        print("\n📝 下一步:")
        print("1. 运行 'streamlit run streamlit_app.py' 测试完整系统")
        print("2. 或者运行 'python rag_system.py' 测试RAG功能")
    else:
        print("⚠️  部分测试失败，请检查:")
        print("1. API密钥是否正确设置")
        print("2. 网络连接是否正常")
        print("3. 是否安装了正确的依赖包")

if __name__ == "__main__":
    main()
