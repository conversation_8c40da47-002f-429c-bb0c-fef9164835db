"""
Streamlit Web界面
用于测试和演示RAG系统
"""

import streamlit as st
import pandas as pd
import plotly.express as px
from rag_system import RAGSystem
import os
from datetime import datetime

# 页面配置
st.set_page_config(
    page_title="马来西亚餐厅RAG系统",
    page_icon="🍽️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 初始化RAG系统
@st.cache_resource
def initialize_rag_system():
    """初始化RAG系统（使用缓存避免重复初始化）"""
    try:
        rag = RAGSystem()
        return rag
    except Exception as e:
        st.error(f"初始化RAG系统失败: {e}")
        return None

def main():
    st.title("🍽️ 马来西亚餐厅RAG系统")
    st.markdown("基于Gemini API和向量数据库的智能餐厅推荐系统")
    
    # 侧边栏
    with st.sidebar:
        st.header("⚙️ 系统设置")
        
        # API密钥设置
        api_key = st.text_input("Google API Key", type="password", 
                               help="请输入您的Google Gemini API密钥")
        
        if api_key:
            os.environ['GOOGLE_API_KEY'] = api_key
        
        # 数据库设置
        st.subheader("📚 数据库设置")
        force_rebuild = st.checkbox("强制重建数据库", help="重新处理数据并重建向量数据库")
        
        if st.button("初始化/重建数据库"):
            if not api_key:
                st.error("请先输入Google API Key")
            else:
                with st.spinner("正在初始化数据库..."):
                    rag = initialize_rag_system()
                    if rag:
                        rag.setup_database(force_rebuild=force_rebuild)
                        st.success("数据库初始化完成！")
                        st.rerun()
    
    # 主界面
    rag = initialize_rag_system()
    
    if not rag:
        st.error("RAG系统未能正确初始化，请检查配置")
        return
    
    # 检查数据库状态
    stats = rag.get_statistics()
    if stats.get('total_restaurants', 0) == 0:
        st.warning("数据库为空，请先在侧边栏初始化数据库")
        return
    
    # 创建标签页
    tab1, tab2, tab3, tab4 = st.tabs(["💬 智能问答", "🔍 餐厅搜索", "📊 数据统计", "ℹ️ 系统信息"])
    
    with tab1:
        st.header("💬 智能餐厅问答")
        st.markdown("问我任何关于马来西亚餐厅的问题！")
        
        # 对话历史
        if 'conversation_history' not in st.session_state:
            st.session_state.conversation_history = []
        
        # 查询输入
        user_query = st.text_input("您的问题:", placeholder="例如：推荐一家好的中餐厅")
        
        col1, col2 = st.columns([1, 4])
        with col1:
            if st.button("🚀 提问", type="primary"):
                if user_query:
                    with st.spinner("正在思考..."):
                        # 生成回答
                        answer = rag.generate_answer(user_query)
                        
                        # 添加到对话历史
                        st.session_state.conversation_history.append({
                            "timestamp": datetime.now().strftime("%H:%M:%S"),
                            "query": user_query,
                            "answer": answer
                        })
        
        with col2:
            if st.button("🗑️ 清空对话"):
                st.session_state.conversation_history = []
                st.rerun()
        
        # 显示对话历史
        if st.session_state.conversation_history:
            st.subheader("对话历史")
            for i, conv in enumerate(reversed(st.session_state.conversation_history)):
                with st.expander(f"[{conv['timestamp']}] {conv['query'][:50]}..."):
                    st.markdown(f"**问题:** {conv['query']}")
                    st.markdown(f"**回答:** {conv['answer']}")
    
    with tab2:
        st.header("🔍 餐厅搜索")
        
        col1, col2 = st.columns([3, 1])
        with col1:
            search_query = st.text_input("搜索餐厅:", placeholder="输入餐厅名称、菜系或其他关键词")
        with col2:
            top_k = st.selectbox("结果数量", [3, 5, 10], index=1)
        
        if st.button("🔍 搜索"):
            if search_query:
                with st.spinner("搜索中..."):
                    results = rag.search_restaurants(search_query, top_k=top_k)
                    
                    if results:
                        st.success(f"找到 {len(results)} 个相关结果")
                        
                        for i, result in enumerate(results, 1):
                            metadata = result['metadata']
                            
                            with st.expander(f"🏪 {metadata.get('restaurant_name', '未知餐厅')} - {metadata.get('cuisine', '未知菜系')}"):
                                col1, col2 = st.columns(2)
                                
                                with col1:
                                    st.markdown(f"**评分:** {metadata.get('rating', '未知')}")
                                    st.markdown(f"**菜系:** {metadata.get('cuisine', '未知')}")
                                    st.markdown(f"**价格:** {metadata.get('price_range', '未知')}")
                                    st.markdown(f"**清真:** {metadata.get('halal_status', '未知')}")
                                
                                with col2:
                                    st.markdown(f"**位置:** {metadata.get('location', '未知')}")
                                    st.markdown(f"**推荐菜品:** {metadata.get('must_try_dishes', '未知')}")
                                
                                if metadata.get('pros'):
                                    st.markdown(f"**优点:** {metadata.get('pros')}")
                                if metadata.get('cons'):
                                    st.markdown(f"**缺点:** {metadata.get('cons')}")
                    else:
                        st.warning("没有找到相关结果")
    
    with tab3:
        st.header("📊 数据统计")
        
        # 显示基本统计
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("总餐厅数", stats.get('total_restaurants', 0))
        with col2:
            avg_rating = stats.get('average_rating')
            if avg_rating:
                st.metric("平均评分", f"{avg_rating:.1f}")
        with col3:
            cuisine_count = len(stats.get('cuisine_distribution', {}))
            st.metric("菜系种类", cuisine_count)
        
        # 菜系分布图
        if 'cuisine_distribution' in stats:
            st.subheader("菜系分布")
            cuisine_df = pd.DataFrame(
                list(stats['cuisine_distribution'].items()),
                columns=['菜系', '餐厅数量']
            )
            
            fig = px.bar(cuisine_df, x='菜系', y='餐厅数量', 
                        title="各菜系餐厅数量分布")
            fig.update_xaxes(tickangle=45)
            st.plotly_chart(fig, use_container_width=True)
    
    with tab4:
        st.header("ℹ️ 系统信息")
        
        st.subheader("🔧 技术栈")
        tech_stack = {
            "向量化模型": rag.embedding_model,
            "向量数据库": "ChromaDB",
            "语言模型": "Google Gemini Pro",
            "Web框架": "Streamlit",
            "数据处理": "Pandas"
        }
        
        for tech, desc in tech_stack.items():
            st.markdown(f"**{tech}:** {desc}")
        
        st.subheader("📁 文件结构")
        files = [
            "rag_system.py - 主RAG系统",
            "data_processor.py - 数据预处理",
            "embedding_manager.py - 向量化管理",
            "vector_database.py - 向量数据库",
            "gemini_client.py - Gemini API客户端",
            "streamlit_app.py - Web界面"
        ]
        
        for file in files:
            st.markdown(f"• {file}")
        
        st.subheader("📊 数据库详情")
        for key, value in stats.items():
            if key not in ['cuisine_distribution']:
                st.markdown(f"**{key}:** {value}")

if __name__ == "__main__":
    main()
