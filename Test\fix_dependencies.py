"""
修复依赖冲突的脚本
"""

import subprocess
import sys
import os

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} 成功")
            return True
        else:
            print(f"❌ {description} 失败:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ {description} 出错: {e}")
        return False

def main():
    """主修复函数"""
    print("🚀 开始修复依赖冲突...\n")
    
    # 步骤1: 升级pip
    run_command(f"{sys.executable} -m pip install --upgrade pip", "升级pip")
    
    # 步骤2: 安装核心依赖
    core_packages = [
        "torch",
        "transformers",
        "sentence-transformers",
        "google-genai",  # 更新为官方包
        "chromadb",
        "streamlit",
        "pandas",
        "numpy<2.0.0",
        "python-dotenv",
        "plotly"
    ]
    
    print("\n📦 安装核心依赖包...")
    for package in core_packages:
        run_command(f"{sys.executable} -m pip install {package}", f"安装 {package}")
    
    # 步骤3: 验证安装
    print("\n🔍 验证安装...")
    test_imports = [
        ("sentence_transformers", "SentenceTransformer"),
        ("google.genai", "genai"),  # 更新导入路径
        ("chromadb", "chromadb"),
        ("streamlit", "st"),
        ("pandas", "pd"),
        ("numpy", "np"),
        ("dotenv", "load_dotenv"),
        ("plotly.express", "px")
    ]
    
    all_success = True
    for module, alias in test_imports:
        try:
            exec(f"import {module} as {alias}")
            print(f"✅ {module} 导入成功")
        except ImportError as e:
            print(f"❌ {module} 导入失败: {e}")
            all_success = False
    
    if all_success:
        print("\n🎉 所有依赖安装成功！")
        print("现在可以运行: streamlit run streamlit_app.py")
    else:
        print("\n⚠️ 部分依赖安装失败，请手动安装")

if __name__ == "__main__":
    main()
